#!/usr/bin/env tsx

import { execSync } from "child_process";

/**
 * 简化的 Pages 部署脚本
 * 只部署 Pages，不创建或修改数据库和 KV
 */

const PROJECT_NAME = process.env.PROJECT_NAME || "moemail";

/**
 * 部署Pages应用
 */
const deployPages = () => {
  console.log("🚧 Deploying to Cloudflare Pages...");
  try {
    execSync(`wrangler pages deploy .vercel/output/static --project-name ${PROJECT_NAME}`, { stdio: "inherit" });
    console.log("✅ Pages deployment completed successfully");
  } catch (error) {
    console.error("❌ Pages deployment failed:", error);
    throw error;
  }
};

/**
 * 主函数
 */
const main = async () => {
  try {
    console.log("🚀 Starting Pages-only deployment...");
    
    // 确保已经构建
    console.log("📦 Building application...");
    execSync("pnpm run build:pages", { stdio: "inherit" });
    
    // 部署
    deployPages();

    console.log("🎉 Pages deployment completed successfully");
  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
};

main();
