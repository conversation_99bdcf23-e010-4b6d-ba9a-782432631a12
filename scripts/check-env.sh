#!/bin/bash

# 环境变量检查脚本
echo "🔍 检查环境变量配置..."
echo "================================"

# 读取 .env.local 文件
if [ ! -f ".env.local" ]; then
    echo "❌ .env.local 文件不存在"
    exit 1
fi

# 检查必需的环境变量
required_vars=(
    "AUTH_GITHUB_ID"
    "AUTH_GITHUB_SECRET" 
    "AUTH_SECRET"
    "CLOUDFLARE_API_TOKEN"
    "CLOUDFLARE_ACCOUNT_ID"
    "DATABASE_NAME"
    "DATABASE_ID"
    "KV_NAMESPACE_NAME"
    "KV_NAMESPACE_ID"
    "PROJECT_NAME"
    "CUSTOM_DOMAIN"
)

missing_vars=()
configured_vars=()

echo "📋 环境变量状态:"
echo "----------------"

for var in "${required_vars[@]}"; do
    value=$(grep "^$var=" .env.local | cut -d'=' -f2- | tr -d '"')
    if [ -z "$value" ]; then
        missing_vars+=("$var")
        echo "❌ $var: 未设置"
    else
        configured_vars+=("$var")
        # 隐藏敏感信息，只显示前几个字符
        if [[ "$var" == *"SECRET"* ]] || [[ "$var" == *"TOKEN"* ]]; then
            masked_value="${value:0:8}..."
            echo "✅ $var: $masked_value"
        else
            echo "✅ $var: $value"
        fi
    fi
done

echo ""
echo "📊 统计:"
echo "--------"
echo "✅ 已配置: ${#configured_vars[@]}/${#required_vars[@]}"
echo "❌ 缺失: ${#missing_vars[@]}/${#required_vars[@]}"

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "⚠️  需要配置的环境变量:"
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "💡 提示: 请在 .env.local 文件中设置这些变量"
    exit 1
else
    echo ""
    echo "🎉 所有环境变量都已配置完成！"
    exit 0
fi
