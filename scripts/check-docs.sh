#!/bin/bash

# 文档完整性检查脚本
echo "📚 检查文档完整性..."

# 检查 README.md 是否包含所有必要的部署脚本说明
echo "🔍 检查 README.md 中的部署脚本说明..."

required_scripts=(
    "deploy:all"
    "deploy:frontend"
    "deploy:workers"
    "deploy:email"
    "deploy:cleanup"
    "deploy:pages"
)

missing_scripts=()

for script in "${required_scripts[@]}"; do
    if ! grep -q "$script" README.md; then
        missing_scripts+=("$script")
    fi
done

if [ ${#missing_scripts[@]} -eq 0 ]; then
    echo "✅ 所有部署脚本都已在 README.md 中说明"
else
    echo "❌ 以下脚本在 README.md 中缺少说明:"
    for script in "${missing_scripts[@]}"; do
        echo "  - $script"
    done
fi

# 检查脚本文件是否存在
echo "🔍 检查部署脚本文件..."

script_files=(
    "scripts/deploy-frontend.sh"
    "scripts/deploy-workers.sh"
    "scripts/deploy-all.sh"
)

missing_files=()

for file in "${script_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ 所有部署脚本文件都存在"
else
    echo "❌ 以下脚本文件不存在:"
    for file in "${missing_files[@]}"; do
        echo "  - $file"
    done
fi

# 检查 package.json 中的脚本
echo "🔍 检查 package.json 中的脚本..."

for script in "${required_scripts[@]}"; do
    if ! grep -q "\"$script\":" package.json; then
        echo "⚠️ package.json 中缺少脚本: $script"
    fi
done

echo "🎉 文档检查完成！"
