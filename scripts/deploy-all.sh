#!/bin/bash

# 完整部署脚本
echo "🚀 开始完整部署..."

# 检查环境变量
if [ -z "$CLOUDFLARE_ACCOUNT_ID" ] || [ -z "$CLOUDFLARE_API_TOKEN" ]; then
    echo "❌ 错误: 请设置 CLOUDFLARE_ACCOUNT_ID 和 CLOUDFLARE_API_TOKEN 环境变量"
    exit 1
fi

# 应用数据库迁移
echo "🗄️ 应用数据库迁移..."
if ! pnpm run db:migrate-remote; then
    echo "⚠️ 数据库迁移失败，但继续部署..."
fi

# 部署前端
echo "📦 构建并部署前端..."
if pnpm run build:pages; then
    wrangler pages deploy .vercel/output/static --project-name ${PROJECT_NAME:-moemail}
    echo "✅ 前端部署成功"
else
    echo "❌ 前端构建失败"
    exit 1
fi

# 部署 Workers
echo "📧 部署 Email Worker..."
if wrangler deploy --config wrangler.email.json; then
    echo "✅ Email Worker 部署成功"
else
    echo "⚠️ Email Worker 部署失败，但继续..."
fi

echo "🧹 部署 Cleanup Worker..."
if wrangler deploy --config wrangler.cleanup.json; then
    echo "✅ Cleanup Worker 部署成功"
else
    echo "⚠️ Cleanup Worker 部署失败"
fi

echo "🎉 完整部署完成！"
echo "🌐 访问: https://bettergpt.eu.org"
