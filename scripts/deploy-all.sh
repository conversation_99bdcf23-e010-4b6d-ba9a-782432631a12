#!/bin/bash

# 完整部署脚本
echo "🚀 开始完整部署..."

# 检查环境变量
if [ -z "$CLOUDFLARE_ACCOUNT_ID" ] || [ -z "$CLOUDFLARE_API_TOKEN" ]; then
    echo "❌ 错误: 请设置 CLOUDFLARE_ACCOUNT_ID 和 CLOUDFLARE_API_TOKEN 环境变量"
    exit 1
fi

# 应用数据库迁移
echo "🗄️ 应用数据库迁移..."
pnpm run db:migrate-remote

# 部署前端
echo "📦 构建并部署前端..."
pnpm run build:pages
wrangler pages deploy .vercel/output/static --project-name ${PROJECT_NAME:-moemail}

# 部署 Workers
echo "📧 部署 Email Worker..."
wrangler deploy --config wrangler.email.json

echo "🧹 部署 Cleanup Worker..."
wrangler deploy --config wrangler.cleanup.json

echo "🎉 完整部署完成！"
echo "🌐 访问: https://bettergpt.eu.org"
