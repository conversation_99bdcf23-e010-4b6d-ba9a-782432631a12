# 邮件批量操作功能演示

## 界面对比

### 普通模式
```
┌─────────────────────────────────────────────────────────┐
│ [🔄] [☐]                           3 封邮件            │
├─────────────────────────────────────────────────────────┤
│ 📧 重要通知 - <EMAIL>     2024-08-01 10:30  🗑️│
│ 📧 会议邀请 - <EMAIL>        2024-08-01 09:15  🗑️│
│ 📧 系统更新 - <EMAIL>    2024-08-01 08:00  🗑️│
└─────────────────────────────────────────────────────────┘
```

### 多选模式
```
┌─────────────────────────────────────────────────────────┐
│ [🔄] [☑]                           已选择 2 封邮件      │
├─────────────────────────────────────────────────────────┤
│ ☑ 全选                                    删除 (2)     │
├─────────────────────────────────────────────────────────┤
│ ☑ 重要通知 - <EMAIL>     2024-08-01 10:30    │
│ ☑ 会议邀请 - <EMAIL>        2024-08-01 09:15    │
│ ☐ 系统更新 - <EMAIL>    2024-08-01 08:00    │
└─────────────────────────────────────────────────────────┘
```

## 操作流程

### 1. 进入多选模式
```
点击: [☐] → [☑]
结果: 显示批量操作工具栏，邮件项显示复选框
```

### 2. 选择邮件
```
方式1: 点击单个邮件的复选框
方式2: 点击"全选"选择所有邮件
结果: 更新选择计数，高亮选中项
```

### 3. 批量删除
```
点击: "删除 (N)" 按钮
过程: 显示"删除中..."，并发删除请求
结果: 显示删除结果，更新邮件列表
```

### 4. 退出多选模式
```
点击: [☑] → [☐]
结果: 隐藏批量操作工具栏，恢复普通显示
```

## 状态变化

### 选择状态
```
无选择:   "3 封邮件"
有选择:   "已选择 2 封邮件"
全选:     "已选择 3 封邮件"
```

### 按钮状态
```
无选择:   "删除 (0)" - 禁用
有选择:   "删除 (2)" - 可用
删除中:   "删除中..." - 禁用
```

## 技术特性

### 🔄 状态同步
- 切换邮箱时自动清理选择状态
- 切换消息类型（收件/发件）时重置选择
- 删除后自动更新选择状态

### 🎯 用户体验
- 视觉反馈：选中项高亮显示
- 操作提示：实时显示选择数量
- 错误处理：删除失败时显示详细信息

### ⚡ 性能优化
- 使用 Set 数据结构管理选择状态
- 并发删除请求提高批量操作速度
- 防抖处理避免重复操作

## 代码示例

### 状态管理
```typescript
const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set())
const [isSelectionMode, setIsSelectionMode] = useState(false)
const [batchDeleting, setBatchDeleting] = useState(false)
```

### 选择切换
```typescript
const toggleMessageSelection = (messageId: string) => {
  setSelectedMessages(prev => {
    const newSet = new Set(prev)
    if (newSet.has(messageId)) {
      newSet.delete(messageId)
    } else {
      newSet.add(messageId)
    }
    return newSet
  })
}
```

### 批量删除
```typescript
const handleBatchDelete = async () => {
  const deletePromises = Array.from(selectedMessages).map(messageId =>
    fetch(`/api/emails/${email.id}/${messageId}`, { method: "DELETE" })
  )
  const results = await Promise.allSettled(deletePromises)
  // 处理结果...
}
```
