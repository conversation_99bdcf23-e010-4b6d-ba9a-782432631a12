# 邮件批量操作功能

## 功能概述

为 MoeMail 邮件列表添加了多选和批量操作功能，支持批量删除邮件。

## 功能特性

### 🔲 多选模式
- 点击邮件列表顶部的方框图标进入/退出多选模式
- 在多选模式下，邮件项左侧显示复选框而不是邮件图标
- 支持单个邮件选择和全选功能

### 🗑️ 批量删除
- 在多选模式下选择邮件后，可以批量删除
- 显示已选择邮件数量
- 支持删除进度提示和结果反馈

### 📱 用户体验
- 智能状态管理：切换邮箱或消息类型时自动清理选择状态
- 视觉反馈：选中的邮件有高亮显示
- 操作提示：工具栏显示选择数量和操作按钮

## 界面变化

### 工具栏
```
[刷新] [多选] ────────────── [邮件数量/选择状态]
```

### 多选模式工具栏
```
[☑ 全选] ──────────────── [删除 (N)]
```

### 邮件项
```
普通模式: [📧] 邮件标题 ──── [🗑️]
多选模式: [☑] 邮件标题
```

## 技术实现

### 状态管理
- `selectedMessages: Set<string>` - 存储选中的邮件ID
- `isSelectionMode: boolean` - 多选模式开关
- `batchDeleting: boolean` - 批量删除状态

### 核心函数
- `toggleSelectionMode()` - 切换多选模式
- `toggleMessageSelection(messageId)` - 切换单个邮件选择状态
- `selectAllMessages()` - 全选/取消全选
- `handleBatchDelete()` - 批量删除处理

### API 调用
- 复用现有的单个邮件删除 API
- 使用 `Promise.allSettled()` 并发处理多个删除请求
- 提供详细的成功/失败反馈

## 使用方法

1. **进入多选模式**
   - 点击邮件列表顶部的方框图标

2. **选择邮件**
   - 点击邮件项选择/取消选择
   - 点击"全选"选择所有邮件

3. **批量删除**
   - 选择要删除的邮件
   - 点击"删除 (N)"按钮
   - 确认删除操作

4. **退出多选模式**
   - 再次点击方框图标
   - 或切换到其他邮箱/消息类型

## 注意事项

- 批量删除操作不可撤销
- 删除过程中会显示进度提示
- 如果部分邮件删除失败，会显示详细的结果统计
- 切换邮箱或消息类型时会自动退出多选模式
